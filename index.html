<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TantaHire - وظيفتك التالية في طنطا</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
            overflow-x: hidden;
        }

        :root {
            --primary-color: #2347a9;
            --secondary-color: #1f2937;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --light-gray: #f8fafc;
            --dark-gray: #64748b;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 2rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navbar {
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .hero-section {
            background: var(--gradient-primary);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>');
            background-size: cover;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-title {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.4rem;
            margin-bottom: 2.5rem;
            opacity: 0.95;
            font-weight: 300;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .btn-hero {
            background: white;
            color: var(--primary-color);
            border: none;
            padding: 18px 45px;
            font-size: 1.2rem;
            font-weight: 700;
            border-radius: 50px;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .btn-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-accent);
            transition: left 0.5s ease;
            z-index: -1;
        }

        .btn-hero:hover::before {
            left: 0;
        }

        .btn-hero:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(0,0,0,0.3);
            color: white;
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 3rem;
            text-align: center;
        }
        
        .job-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            margin-bottom: 2rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .job-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .job-card:hover::before {
            transform: scaleX(1);
        }

        .job-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .job-title {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.3rem;
        }
        
        .company-name {
            color: var(--dark-gray);
            font-weight: 500;
        }
        
        .job-location {
            color: var(--accent-color);
            font-weight: 500;
        }
        
        .job-type {
            background: var(--gradient-accent);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .job-type.part-time {
            background: var(--gradient-secondary);
        }

        .job-type.remote {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .btn-apply {
            background: var(--gradient-primary);
            border: none;
            border-radius: 30px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-apply::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-secondary);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .btn-apply:hover::before {
            left: 0;
        }

        .btn-apply:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .feature-card {
            text-align: center;
            padding: 3rem 2rem;
            border-radius: 25px;
            background: white;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-accent);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .feature-card:hover::before {
            opacity: 0.05;
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .feature-card > * {
            position: relative;
            z-index: 2;
        }
        
        .feature-icon {
            font-size: 4rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
            transition: transform 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }
        
        .why-us-section {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
            padding: 100px 0;
            position: relative;
        }

        .why-us-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="none"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .jobs-section {
            padding: 100px 0;
            background: white;
        }

        .stats-section {
            background: var(--gradient-primary);
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .stat-item {
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Search Section Styles */
        .search-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .search-card {
            border: none;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }

        /* Job Card Enhancements */
        .salary-badge {
            background: var(--gradient-accent);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .salary-badge.daily {
            background: var(--gradient-secondary);
        }

        .job-meta {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .experience-badge {
            background: #e2e8f0;
            color: var(--secondary-color);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .experience-badge.no-exp {
            background: #dcfce7;
            color: #166534;
        }

        .job-requirements {
            border-top: 1px solid #e2e8f0;
            padding-top: 10px;
        }

        .daily-work {
            border-left: 4px solid var(--warning-color);
        }

        .job-type.daily {
            background: var(--gradient-secondary);
        }

        /* Hero Search Styles */
        .hero-search-container {
            margin: 3rem 0;
        }

        .hero-search-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hero-search-card .form-control,
        .hero-search-card .form-select {
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 1rem;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .hero-search-card .form-control:focus,
        .hero-search-card .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(35, 71, 169, 0.25);
        }

        .hero-search-card .input-group-text {
            border-radius: 15px 0 0 15px;
            border: 2px solid #e2e8f0;
            border-right: none;
        }

        .hero-search-card .form-control.border-start-0 {
            border-radius: 0 15px 15px 0;
            border-left: none;
        }

        /* Hero Stats */
        .hero-stats {
            margin-top: 4rem;
        }

        .hero-stat-item {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .hero-stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .hero-stat-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 0;
            color: white;
        }
        
        .footer {
            background-color: var(--secondary-color);
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .footer a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            transition: color 0.3s ease;
        }
        
        .footer a:hover {
            color: var(--accent-color);
        }
        
        .social-icons {
            font-size: 1.5rem;
            margin-top: 1rem;
        }
        
        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .hero-title {
                font-size: 3.5rem;
            }

            .feature-card {
                padding: 2.5rem 2rem;
            }
        }

        @media (max-width: 992px) {
            .hero-title {
                font-size: 3rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .stats-section {
                padding: 60px 0;
            }

            .stat-number {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 100px 0 60px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2rem;
                margin-bottom: 2rem;
            }

            .btn-hero {
                padding: 15px 35px;
                font-size: 1.1rem;
            }

            .hero-search-card {
                padding: 1.5rem;
                margin: 0 10px;
            }

            .hero-search-card .row {
                gap: 15px;
            }

            .hero-search-card .form-control,
            .hero-search-card .form-select {
                padding: 12px 15px;
                font-size: 0.9rem;
            }

            .hero-stat-number {
                font-size: 1.5rem;
            }

            .hero-stat-label {
                font-size: 0.8rem;
            }

            .feature-card {
                padding: 2rem 1.5rem;
                margin-bottom: 2rem;
            }

            .feature-icon {
                font-size: 3rem;
            }

            .jobs-section, .why-us-section {
                padding: 60px 0;
            }

            .stat-number {
                font-size: 2rem;
            }

            .stat-label {
                font-size: 1rem;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .btn-hero {
                padding: 12px 30px;
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .hero-search-card {
                padding: 1rem;
                margin: 0 5px;
            }

            .hero-search-card .row {
                gap: 10px;
            }

            .hero-search-card .col-md-6,
            .hero-search-card .col-md-3 {
                width: 100%;
                margin-bottom: 10px;
            }

            .hero-stats {
                margin-top: 2rem;
            }

            .hero-stat-item {
                padding: 0.8rem;
                margin-bottom: 0.5rem;
            }

            .hero-stat-number {
                font-size: 1.3rem;
            }

            .hero-stat-label {
                font-size: 0.75rem;
            }

            .job-card {
                margin-bottom: 1.5rem;
            }

            .feature-card {
                padding: 1.5rem;
            }

            .feature-icon {
                font-size: 2.5rem;
            }

            .stat-item {
                padding: 1rem;
            }

            .stat-number {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-briefcase-fill me-2"></i>
                TantaHire
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">عنّا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#jobs">الوظائف</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">تواصل معنا</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="hero-content">
                        <h1 class="hero-title">وظيفتك التالية في طنطا تبدأ من هنا!</h1>
                        <p class="hero-subtitle">
                            اكتشف أفضل الفرص الوظيفية في مدينة طنطا واحصل على الوظيفة التي تحلم بها
                        </p>

                        <!-- Hero Search Bar -->
                        <div class="hero-search-container">
                            <div class="hero-search-card">
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <span class="input-group-text bg-white border-end-0">
                                                <i class="bi bi-search text-primary"></i>
                                            </span>
                                            <input type="text" class="form-control border-start-0"
                                                   id="heroSearch" placeholder="ابحث عن وظيفة أو شركة...">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="heroJobType">
                                            <option value="">نوع العمل</option>
                                            <option value="full-time">دوام كامل</option>
                                            <option value="part-time">دوام جزئي</option>
                                            <option value="remote">عن بُعد</option>
                                            <option value="daily">أعمال يومية</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-primary w-100" id="heroSearchBtn">
                                            <i class="bi bi-search me-2"></i>
                                            ابحث الآن
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="hero-stats">
                            <div class="row text-center">
                                <div class="col-6 col-md-3">
                                    <div class="hero-stat-item">
                                        <h3 class="hero-stat-number">1250+</h3>
                                        <p class="hero-stat-label">وظيفة متاحة</p>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="hero-stat-item">
                                        <h3 class="hero-stat-number">850+</h3>
                                        <p class="hero-stat-label">شركة مسجلة</p>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="hero-stat-item">
                                        <h3 class="hero-stat-number">5200+</h3>
                                        <p class="hero-stat-label">باحث عن عمل</p>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="hero-stat-item">
                                        <h3 class="hero-stat-number">3400+</h3>
                                        <p class="hero-stat-label">وظيفة تم شغلها</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Search and Filter Section -->
    <section class="search-section py-5 bg-light">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="search-card p-4 bg-white rounded-4 shadow">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label fw-bold">البحث عن وظيفة</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="jobSearch" placeholder="اكتب اسم الوظيفة أو الشركة...">
                                    <button class="btn btn-primary" type="button" id="searchBtn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">نوع العمل</label>
                                <select class="form-select" id="jobTypeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="full-time">دوام كامل</option>
                                    <option value="part-time">دوام جزئي</option>
                                    <option value="remote">عن بُعد</option>
                                    <option value="daily">أعمال يومية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label fw-bold">المجال</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع المجالات</option>
                                    <option value="tech">تكنولوجيا</option>
                                    <option value="finance">مالية ومحاسبة</option>
                                    <option value="marketing">تسويق</option>
                                    <option value="design">تصميم</option>
                                    <option value="education">تعليم</option>
                                    <option value="engineering">هندسة</option>
                                    <option value="medical">طبي وصيدلة</option>
                                    <option value="sales">مبيعات</option>
                                    <option value="daily-work">أعمال يومية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label fw-bold">الخبرة</label>
                                <select class="form-select" id="experienceFilter">
                                    <option value="">جميع المستويات</option>
                                    <option value="no-experience">بدون خبرة</option>
                                    <option value="1-2">1-2 سنة</option>
                                    <option value="3-5">3-5 سنوات</option>
                                    <option value="5+">أكثر من 5 سنوات</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button class="btn btn-outline-secondary me-2" id="clearFilters">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    مسح الفلاتر
                                </button>
                                <span class="text-muted" id="resultsCount">عرض جميع الوظائف</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Jobs Section -->
    <section id="jobs" class="jobs-section">
        <div class="container">
            <h2 class="section-title">الوظائف المتاحة حالياً</h2>
            
            <div class="row" id="jobsContainer">
                <!-- Job Card 1 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="tech" data-type="full-time" data-experience="3-5">
                    <div class="card job-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">مطور ويب Frontend</h5>
                                <span class="salary-badge">15,000 - 20,000 ج.م</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة التقنيات المتقدمة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type mb-2">دوام كامل</span>
                                <span class="experience-badge">3-5 سنوات خبرة</span>
                            </div>
                            <p class="card-text">
                                مطلوب مطور ويب متخصص في React وVue.js للعمل في بيئة تقنية متطورة.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    React, Vue.js, JavaScript
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="1">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(1)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Card 2 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="finance" data-type="full-time" data-experience="1-2">
                    <div class="card job-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">محاسب مالي</h5>
                                <span class="salary-badge">8,000 - 12,000 ج.م</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                مجموعة النيل التجارية
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type mb-2">دوام كامل</span>
                                <span class="experience-badge">1-2 سنة خبرة</span>
                            </div>
                            <p class="card-text">
                                فرصة ممتازة للعمل كمحاسب مالي في شركة رائدة مع راتب تنافسي ومزايا إضافية.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Excel, محاسبة مالية, تقارير
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="2">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(2)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Card 3 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="marketing" data-type="part-time" data-experience="1-2">
                    <div class="card job-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">مدير تسويق رقمي</h5>
                                <span class="salary-badge">6,000 - 10,000 ج.م</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                وكالة الإبداع الرقمي
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type part-time mb-2">دوام جزئي</span>
                                <span class="experience-badge">1-2 سنة خبرة</span>
                            </div>
                            <p class="card-text">
                                انضم إلى فريقنا كمدير تسويق رقمي وساعد في نمو العلامات التجارية المحلية.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Social Media, Google Ads, SEO
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="3">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(3)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Card 4 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="design" data-type="full-time" data-experience="1-2">
                    <div class="card job-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">مصمم جرافيك</h5>
                                <span class="salary-badge">7,000 - 12,000 ج.م</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                استوديو الفن الحديث
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type mb-2">دوام كامل</span>
                                <span class="experience-badge">1-2 سنة خبرة</span>
                            </div>
                            <p class="card-text">
                                مطلوب مصمم جرافيك مبدع للعمل على مشاريع متنوعة في بيئة إبداعية محفزة.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    Photoshop, Illustrator, InDesign
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="4">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(4)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Card 5 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="education" data-type="part-time" data-experience="1-2">
                    <div class="card job-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">مدرس لغة إنجليزية</h5>
                                <span class="salary-badge">5,000 - 8,000 ج.م</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                معهد اللغات الدولي
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type part-time mb-2">دوام جزئي</span>
                                <span class="experience-badge">1-2 سنة خبرة</span>
                            </div>
                            <p class="card-text">
                                فرصة ممتازة لتدريس اللغة الإنجليزية للطلاب من مختلف الأعمار والمستويات.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    إتقان اللغة الإنجليزية، شهادة تدريس
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="5">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(5)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Card 6 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="tech" data-type="remote" data-experience="3-5">
                    <div class="card job-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">مطور تطبيقات موبايل</h5>
                                <span class="salary-badge">18,000 - 25,000 ج.م</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة التطبيقات الذكية
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية / عن بُعد
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type remote mb-2">عن بُعد</span>
                                <span class="experience-badge">3-5 سنوات خبرة</span>
                            </div>
                            <p class="card-text">
                                انضم لفريقنا لتطوير تطبيقات موبايل مبتكرة باستخدام React Native وFlutter.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    React Native, Flutter, Firebase
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="6">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(6)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Job Card 7 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مهندس مدني</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة المقاولات المتطورة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                مطلوب مهندس مدني خبرة 2-5 سنوات للعمل على مشاريع إنشائية متنوعة.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 8 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">صيدلي</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                صيدلية النور الطبية
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                فرصة عمل ممتازة لصيدلي مؤهل في صيدلية حديثة ومجهزة بأحدث التقنيات.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 9 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مندوب مبيعات</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة التوزيع الحديثة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                انضم لفريق المبيعات النشط واحصل على راتب أساسي + عمولة مجزية.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Daily Work Card 1 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="daily-work" data-type="daily" data-experience="no-experience">
                    <div class="card job-card daily-work">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">عامل تنظيف مكاتب</h5>
                                <span class="salary-badge daily">200 ج.م / يوم</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة الخدمات المتكاملة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type daily mb-2">عمل يومي</span>
                                <span class="experience-badge no-exp">بدون خبرة</span>
                            </div>
                            <p class="card-text">
                                مطلوب عمال تنظيف للعمل في المكاتب والشركات. عمل يومي بأجر يومي.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    لا يتطلب خبرة سابقة
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="10">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(10)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Daily Work Card 2 -->
                <div class="col-lg-4 col-md-6 job-item" data-category="daily-work" data-type="daily" data-experience="no-experience">
                    <div class="card job-card daily-work">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="job-title">عامل بناء</h5>
                                <span class="salary-badge daily">300 ج.م / يوم</span>
                            </div>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                مقاولات الإنشاء الحديثة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <div class="job-meta mb-3">
                                <span class="job-type daily mb-2">عمل يومي</span>
                                <span class="experience-badge no-exp">بدون خبرة</span>
                            </div>
                            <p class="card-text">
                                مطلوب عمال بناء للعمل في مشاريع إنشائية. أجر يومي مجزي.
                            </p>
                            <div class="job-requirements mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-check-circle me-1"></i>
                                    قوة بدنية، استعداد للعمل الشاق
                                </small>
                            </div>
                            <div class="d-grid gap-2">
                                <button class="btn btn-apply btn-primary" data-job-id="11">
                                    <i class="bi bi-send me-2"></i>
                                    تقدم الآن
                                </button>
                                <button class="btn btn-outline-primary btn-sm" onclick="showJobDetails(11)">
                                    <i class="bi bi-eye me-1"></i>
                                    عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <a href="#" class="btn btn-outline-primary btn-lg px-5 py-3">
                    <i class="bi bi-search me-2"></i>
                    عرض جميع الوظائف
                    <i class="bi bi-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="1250">0</span>
                        <div class="stat-label">وظيفة متاحة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="850">0</span>
                        <div class="stat-label">شركة مسجلة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="5200">0</span>
                        <div class="stat-label">باحث عن عمل</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="3400">0</span>
                        <div class="stat-label">وظيفة تم شغلها</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Us Section -->
    <section id="about" class="why-us-section">
        <div class="container">
            <h2 class="section-title">لماذا TantaHire؟</h2>

            <div class="row">
                <!-- Feature 1 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-lightning-charge"></i>
                        </div>
                        <h4 class="feature-title">سهولة الاستخدام</h4>
                        <p class="text-muted">
                            واجهة بسيطة وسهلة تمكنك من البحث عن الوظائف والتقديم عليها في دقائق معدودة
                        </p>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-arrow-clockwise"></i>
                        </div>
                        <h4 class="feature-title">تحديث دائم</h4>
                        <p class="text-muted">
                            نضيف وظائف جديدة يومياً ونحدث قاعدة البيانات باستمرار لضمان أحدث الفرص المتاحة
                        </p>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-collection"></i>
                        </div>
                        <h4 class="feature-title">فرص متنوعة</h4>
                        <p class="text-muted">
                            نوفر وظائف في جميع المجالات والتخصصات لتناسب مختلف المؤهلات والخبرات
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5">
        <div class="container">
            <h2 class="section-title">تواصل معنا</h2>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card border-0 shadow-lg">
                        <div class="card-body p-5">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-envelope"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">البريد الإلكتروني</h5>
                                            <p class="text-muted mb-0"><EMAIL></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-telephone"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">الهاتف</h5>
                                            <p class="text-muted mb-0">+20 40 123 4567</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-geo-alt"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">العنوان</h5>
                                            <p class="text-muted mb-0">طنطا، محافظة الغربية، مصر</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-clock"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">ساعات العمل</h5>
                                            <p class="text-muted mb-0">السبت - الخميس: 9:00 - 17:00</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h5 class="mb-3">
                        <i class="bi bi-briefcase-fill me-2"></i>
                        TantaHire
                    </h5>
                    <p class="mb-3">
                        منصة التوظيف الأولى في مدينة طنطا، نربط بين أصحاب العمل والباحثين عن الوظائف
                    </p>
                </div>

                <div class="col-lg-6">
                    <h6 class="mb-3">تابعنا على</h6>
                    <div class="social-icons">
                        <a href="#" class="me-3">
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-twitter"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-linkedin"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.2);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 TantaHire. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="me-3">سياسة الخصوصية</a>
                    <a href="#" class="me-3">الشروط والأحكام</a>
                    <a href="#">المساعدة</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Job Details Modal -->
    <div class="modal fade" id="jobDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="jobModalTitle">تفاصيل الوظيفة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="jobModalBody">
                    <!-- Job details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="showApplicationForm()">تقدم للوظيفة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Form Modal -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تقديم طلب توظيف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="applicationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" name="fullName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" name="phone" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">العمر</label>
                                <input type="number" class="form-control" name="age" min="18" max="65">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" name="address" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">سنوات الخبرة</label>
                            <select class="form-select" name="experience">
                                <option value="">اختر مستوى الخبرة</option>
                                <option value="no-experience">بدون خبرة</option>
                                <option value="1-2">1-2 سنة</option>
                                <option value="3-5">3-5 سنوات</option>
                                <option value="5+">أكثر من 5 سنوات</option>
                            </select>
                        </div>
                        <div class="mb-3" id="cvUploadSection">
                            <label class="form-label">رفع السيرة الذاتية (PDF)</label>
                            <input type="file" class="form-control" name="cv" accept=".pdf">
                            <small class="text-muted">اختياري للأعمال اليومية، مطلوب للوظائف الأخرى</small>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">لماذا تريد هذه الوظيفة؟</label>
                            <textarea class="form-control" name="motivation" rows="3" placeholder="اكتب سبب اهتمامك بهذه الوظيفة..."></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="terms" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    أوافق على <a href="#" class="text-primary">الشروط والأحكام</a>
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="submitApplication()">إرسال الطلب</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Enhanced JavaScript -->
    <script>
        // Job data
        const jobsData = {
            1: {
                title: "مطور ويب Frontend",
                company: "شركة التقنيات المتقدمة",
                location: "طنطا، الغربية",
                type: "دوام كامل",
                salary: "15,000 - 20,000 ج.م",
                experience: "3-5 سنوات",
                category: "تكنولوجيا",
                description: "نبحث عن مطور ويب Frontend محترف للانضمام إلى فريقنا التقني المتميز.",
                requirements: [
                    "خبرة 3-5 سنوات في تطوير الويب",
                    "إتقان React.js و Vue.js",
                    "معرفة قوية بـ HTML5, CSS3, JavaScript",
                    "خبرة في استخدام Git",
                    "القدرة على العمل ضمن فريق"
                ],
                responsibilities: [
                    "تطوير واجهات المستخدم التفاعلية",
                    "تحسين أداء المواقع",
                    "التعاون مع فريق التصميم",
                    "كتابة كود نظيف وقابل للصيانة"
                ],
                needsCV: true
            },
            2: {
                title: "محاسب مالي",
                company: "مجموعة النيل التجارية",
                location: "طنطا، الغربية",
                type: "دوام كامل",
                salary: "8,000 - 12,000 ج.م",
                experience: "1-2 سنة",
                category: "مالية ومحاسبة",
                description: "فرصة ممتازة للعمل كمحاسب مالي في شركة رائدة.",
                requirements: [
                    "شهادة في المحاسبة أو المالية",
                    "خبرة 1-2 سنة في المحاسبة",
                    "إتقان Excel والبرامج المحاسبية",
                    "دقة في العمل والتفاصيل"
                ],
                responsibilities: [
                    "إعداد التقارير المالية",
                    "متابعة الحسابات",
                    "إدارة الفواتير والمدفوعات"
                ],
                needsCV: true
            },
            10: {
                title: "عامل تنظيف مكاتب",
                company: "شركة الخدمات المتكاملة",
                location: "طنطا، الغربية",
                type: "عمل يومي",
                salary: "200 ج.م / يوم",
                experience: "بدون خبرة",
                category: "أعمال يومية",
                description: "مطلوب عمال تنظيف للعمل في المكاتب والشركات. عمل يومي بأجر يومي.",
                requirements: [
                    "لا يتطلب خبرة سابقة",
                    "النظافة الشخصية",
                    "الالتزام بالمواعيد",
                    "القدرة على العمل البدني"
                ],
                responsibilities: [
                    "تنظيف المكاتب والممرات",
                    "تنظيف الحمامات",
                    "ترتيب وتنظيم المساحات"
                ],
                needsCV: false
            },
            11: {
                title: "عامل بناء",
                company: "مقاولات الإنشاء الحديثة",
                location: "طنطا، الغربية",
                type: "عمل يومي",
                salary: "300 ج.م / يوم",
                experience: "بدون خبرة",
                category: "أعمال يومية",
                description: "مطلوب عمال بناء للعمل في مشاريع إنشائية. أجر يومي مجزي.",
                requirements: [
                    "قوة بدنية جيدة",
                    "استعداد للعمل الشاق",
                    "الالتزام بقواعد السلامة",
                    "العمل في فريق"
                ],
                responsibilities: [
                    "أعمال البناء والإنشاء",
                    "نقل المواد",
                    "مساعدة الحرفيين المتخصصين"
                ],
                needsCV: false
            },
            3: {
                title: "مدير تسويق رقمي",
                company: "وكالة الإبداع الرقمي",
                location: "طنطا، الغربية",
                type: "دوام جزئي",
                salary: "6,000 - 10,000 ج.م",
                experience: "1-2 سنة",
                category: "تسويق",
                description: "انضم إلى فريقنا كمدير تسويق رقمي وساعد في نمو العلامات التجارية المحلية.",
                requirements: [
                    "خبرة 1-2 سنة في التسويق الرقمي",
                    "إتقان إدارة وسائل التواصل الاجتماعي",
                    "معرفة بـ Google Ads و Facebook Ads",
                    "مهارات في SEO وتحليل البيانات"
                ],
                responsibilities: [
                    "إدارة حملات التسويق الرقمي",
                    "إنشاء محتوى إبداعي",
                    "تحليل أداء الحملات",
                    "التواصل مع العملاء"
                ],
                needsCV: true
            },
            12: {
                title: "عامل مطعم",
                company: "مطعم الأصالة",
                location: "طنطا، الغربية",
                type: "عمل يومي",
                salary: "180 ج.م / يوم",
                experience: "بدون خبرة",
                category: "أعمال يومية",
                description: "مطلوب عمال للعمل في المطعم (تنظيف، تحضير، خدمة). عمل يومي مرن.",
                requirements: [
                    "النظافة الشخصية",
                    "حسن المظهر",
                    "الالتزام بالمواعيد",
                    "القدرة على العمل تحت الضغط"
                ],
                responsibilities: [
                    "تنظيف المطعم",
                    "مساعدة في تحضير الطعام",
                    "خدمة العملاء",
                    "ترتيب الطاولات"
                ],
                needsCV: false
            },
            4: {
                title: "مصمم جرافيك",
                company: "استوديو الفن الحديث",
                location: "طنطا، الغربية",
                type: "دوام كامل",
                salary: "7,000 - 12,000 ج.م",
                experience: "1-2 سنة",
                category: "تصميم",
                description: "مطلوب مصمم جرافيك مبدع للعمل على مشاريع متنوعة في بيئة إبداعية محفزة.",
                requirements: [
                    "خبرة 1-2 سنة في التصميم الجرافيكي",
                    "إتقان Adobe Photoshop و Illustrator",
                    "معرفة بـ InDesign و After Effects",
                    "حس فني وإبداعي عالي"
                ],
                responsibilities: [
                    "تصميم المواد الإعلانية",
                    "إنشاء الهوية البصرية للعلامات التجارية",
                    "تصميم المطبوعات والرقميات",
                    "التعاون مع فريق التسويق"
                ],
                needsCV: true
            },
            5: {
                title: "مدرس لغة إنجليزية",
                company: "معهد اللغات الدولي",
                location: "طنطا، الغربية",
                type: "دوام جزئي",
                salary: "5,000 - 8,000 ج.م",
                experience: "1-2 سنة",
                category: "تعليم",
                description: "فرصة ممتازة لتدريس اللغة الإنجليزية للطلاب من مختلف الأعمار والمستويات.",
                requirements: [
                    "إتقان اللغة الإنجليزية تحدثاً وكتابة",
                    "شهادة في تدريس اللغة الإنجليزية",
                    "خبرة في التدريس",
                    "صبر ومهارات تواصل ممتازة"
                ],
                responsibilities: [
                    "تدريس اللغة الإنجليزية",
                    "إعداد المناهج والخطط الدراسية",
                    "تقييم الطلاب",
                    "متابعة تقدم الطلاب"
                ],
                needsCV: true
            },
            6: {
                title: "مطور تطبيقات موبايل",
                company: "شركة التطبيقات الذكية",
                location: "طنطا، الغربية / عن بُعد",
                type: "عن بُعد",
                salary: "18,000 - 25,000 ج.م",
                experience: "3-5 سنوات",
                category: "تكنولوجيا",
                description: "انضم لفريقنا لتطوير تطبيقات موبايل مبتكرة باستخدام React Native وFlutter.",
                requirements: [
                    "خبرة 3-5 سنوات في تطوير تطبيقات الموبايل",
                    "إتقان React Native أو Flutter",
                    "معرفة بـ Firebase و APIs",
                    "خبرة في نشر التطبيقات على المتاجر"
                ],
                responsibilities: [
                    "تطوير تطبيقات الموبايل",
                    "تحسين أداء التطبيقات",
                    "التعامل مع APIs",
                    "اختبار وصيانة التطبيقات"
                ],
                needsCV: true
            }
        };

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            // Navbar background change
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Active navigation highlighting
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Animated counter for stats
        function animateCounter(element) {
            const target = parseInt(element.getAttribute('data-count'));
            const increment = target / 100;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 20);
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Animate stats counters
                    if (entry.target.classList.contains('stats-section')) {
                        const counters = entry.target.querySelectorAll('.stat-number');
                        counters.forEach(counter => {
                            animateCounter(counter);
                        });
                    }

                    // Add fade-in animation to job cards
                    if (entry.target.classList.contains('job-card')) {
                        entry.target.style.opacity = '0';
                        entry.target.style.transform = 'translateY(30px)';
                        setTimeout(() => {
                            entry.target.style.transition = 'all 0.6s ease';
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, 100);
                    }

                    // Add fade-in animation to feature cards
                    if (entry.target.classList.contains('feature-card')) {
                        entry.target.style.opacity = '0';
                        entry.target.style.transform = 'translateY(30px)';
                        setTimeout(() => {
                            entry.target.style.transition = 'all 0.6s ease';
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, 200);
                    }

                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize search and filter
            initializeSearchAndFilter();

            // Observe stats section
            const statsSection = document.querySelector('.stats-section');
            if (statsSection) observer.observe(statsSection);

            // Observe job cards
            document.querySelectorAll('.job-card').forEach(card => {
                observer.observe(card);
            });

            // Observe feature cards
            document.querySelectorAll('.feature-card').forEach(card => {
                observer.observe(card);
            });
        });

        // Search and Filter functionality
        function initializeSearchAndFilter() {
            const searchInput = document.getElementById('jobSearch');
            const searchBtn = document.getElementById('searchBtn');
            const typeFilter = document.getElementById('jobTypeFilter');
            const categoryFilter = document.getElementById('categoryFilter');
            const experienceFilter = document.getElementById('experienceFilter');
            const clearBtn = document.getElementById('clearFilters');
            const resultsCount = document.getElementById('resultsCount');

            // Hero search elements
            const heroSearch = document.getElementById('heroSearch');
            const heroJobType = document.getElementById('heroJobType');
            const heroSearchBtn = document.getElementById('heroSearchBtn');

            function filterJobs() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedType = typeFilter.value;
                const selectedCategory = categoryFilter.value;
                const selectedExperience = experienceFilter.value;

                const jobItems = document.querySelectorAll('.job-item');
                let visibleCount = 0;

                jobItems.forEach(item => {
                    const title = item.querySelector('.job-title').textContent.toLowerCase();
                    const company = item.querySelector('.company-name').textContent.toLowerCase();
                    const type = item.dataset.type;
                    const category = item.dataset.category;
                    const experience = item.dataset.experience;

                    const matchesSearch = !searchTerm || title.includes(searchTerm) || company.includes(searchTerm);
                    const matchesType = !selectedType || type === selectedType;
                    const matchesCategory = !selectedCategory || category === selectedCategory;
                    const matchesExperience = !selectedExperience || experience === selectedExperience;

                    if (matchesSearch && matchesType && matchesCategory && matchesExperience) {
                        item.style.display = 'block';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                });

                resultsCount.textContent = `عرض ${visibleCount} وظيفة`;
            }

            searchBtn.addEventListener('click', filterJobs);
            searchInput.addEventListener('input', filterJobs);
            typeFilter.addEventListener('change', filterJobs);
            categoryFilter.addEventListener('change', filterJobs);
            experienceFilter.addEventListener('change', filterJobs);

            clearBtn.addEventListener('click', () => {
                searchInput.value = '';
                typeFilter.value = '';
                categoryFilter.value = '';
                experienceFilter.value = '';
                heroSearch.value = '';
                heroJobType.value = '';
                filterJobs();
            });

            // Hero search functionality
            function performHeroSearch() {
                // Copy hero search values to main filters
                searchInput.value = heroSearch.value;
                typeFilter.value = heroJobType.value;

                // Scroll to jobs section
                const jobsSection = document.getElementById('jobs');
                const offsetTop = jobsSection.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });

                // Apply filters
                setTimeout(() => {
                    filterJobs();
                }, 500);
            }

            heroSearchBtn.addEventListener('click', performHeroSearch);
            heroSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performHeroSearch();
                }
            });

            // Sync hero search with main search
            heroSearch.addEventListener('input', () => {
                searchInput.value = heroSearch.value;
            });

            heroJobType.addEventListener('change', () => {
                typeFilter.value = heroJobType.value;
            });
        }

        // Show job details
        function showJobDetails(jobId) {
            const job = jobsData[jobId];
            if (!job) return;

            const modalTitle = document.getElementById('jobModalTitle');
            const modalBody = document.getElementById('jobModalBody');

            modalTitle.textContent = job.title;
            modalBody.innerHTML = `
                <div class="job-details">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6><i class="bi bi-building me-2"></i>الشركة</h6>
                            <p>${job.company}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-geo-alt me-2"></i>الموقع</h6>
                            <p>${job.location}</p>
                        </div>
                    </div>
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <h6><i class="bi bi-clock me-2"></i>نوع العمل</h6>
                            <p>${job.type}</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="bi bi-currency-exchange me-2"></i>الراتب</h6>
                            <p>${job.salary}</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="bi bi-person-check me-2"></i>الخبرة المطلوبة</h6>
                            <p>${job.experience}</p>
                        </div>
                    </div>
                    <div class="mb-4">
                        <h6><i class="bi bi-info-circle me-2"></i>وصف الوظيفة</h6>
                        <p>${job.description}</p>
                    </div>
                    <div class="mb-4">
                        <h6><i class="bi bi-list-check me-2"></i>المتطلبات</h6>
                        <ul>
                            ${job.requirements.map(req => `<li>${req}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="mb-4">
                        <h6><i class="bi bi-briefcase me-2"></i>المسؤوليات</h6>
                        <ul>
                            ${job.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
                        </ul>
                    </div>
                    ${job.needsCV ? '<div class="alert alert-info"><i class="bi bi-file-earmark-pdf me-2"></i>هذه الوظيفة تتطلب رفع السيرة الذاتية</div>' : '<div class="alert alert-success"><i class="bi bi-check-circle me-2"></i>لا تتطلب هذه الوظيفة سيرة ذاتية</div>'}
                </div>
            `;

            // Store current job ID for application
            document.getElementById('jobDetailsModal').dataset.currentJobId = jobId;

            const modal = new bootstrap.Modal(document.getElementById('jobDetailsModal'));
            modal.show();
        }

        // Show application form
        function showApplicationForm() {
            const jobId = document.getElementById('jobDetailsModal').dataset.currentJobId;
            const job = jobsData[jobId];

            // Hide job details modal
            bootstrap.Modal.getInstance(document.getElementById('jobDetailsModal')).hide();

            // Update CV requirement
            const cvSection = document.getElementById('cvUploadSection');
            const cvInput = cvSection.querySelector('input[name="cv"]');
            if (job.needsCV) {
                cvInput.required = true;
                cvSection.querySelector('small').textContent = 'مطلوب رفع السيرة الذاتية (PDF)';
            } else {
                cvInput.required = false;
                cvSection.querySelector('small').textContent = 'اختياري للأعمال اليومية';
            }

            // Store job ID in form
            document.getElementById('applicationForm').dataset.jobId = jobId;

            // Show application modal
            const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
            modal.show();
        }

        // Submit application
        function submitApplication() {
            const form = document.getElementById('applicationForm');
            const formData = new FormData(form);
            const jobId = form.dataset.jobId;
            const job = jobsData[jobId];

            // Validate required fields
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Show loading
            const submitBtn = event.target;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإرسال...';
            submitBtn.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Hide modal
                bootstrap.Modal.getInstance(document.getElementById('applicationModal')).hide();

                // Show success message
                showNotification(`تم إرسال طلبك لوظيفة "${job.title}" بنجاح! سيتم التواصل معك قريباً.`, 'success');

                // Reset form
                form.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        }

        // Enhanced job application functionality
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-apply') || e.target.closest('.btn-apply')) {
                e.preventDefault();
                const button = e.target.classList.contains('btn-apply') ? e.target : e.target.closest('.btn-apply');
                const jobId = button.dataset.jobId;

                // Store job ID and show application form directly
                document.getElementById('applicationForm').dataset.jobId = jobId;
                const job = jobsData[jobId];

                // Update CV requirement
                const cvSection = document.getElementById('cvUploadSection');
                const cvInput = cvSection.querySelector('input[name="cv"]');
                if (job && job.needsCV) {
                    cvInput.required = true;
                    cvSection.querySelector('small').textContent = 'مطلوب رفع السيرة الذاتية (PDF)';
                } else {
                    cvInput.required = false;
                    cvSection.querySelector('small').textContent = 'اختياري للأعمال اليومية';
                }

                const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
                modal.show();
            }
        });

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                border: none;
                border-radius: 10px;
                animation: slideInRight 0.5s ease;
            `;
            notification.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.5s ease';
                    setTimeout(() => notification.remove(), 500);
                }
            }, 5000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
