<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TantaHire - وظيفتك التالية في طنطا</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
        }
        
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --light-gray: #f8f9fa;
            --dark-gray: #6c757d;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: var(--primary-color) !important;
        }
        
        .navbar {
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            background-color: white !important;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .btn-hero {
            background-color: white;
            color: var(--primary-color);
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn-hero:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            color: var(--primary-color);
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 3rem;
            text-align: center;
        }
        
        .job-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 2rem;
        }
        
        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .job-title {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.3rem;
        }
        
        .company-name {
            color: var(--dark-gray);
            font-weight: 500;
        }
        
        .job-location {
            color: var(--accent-color);
            font-weight: 500;
        }
        
        .job-type {
            background-color: var(--light-gray);
            color: var(--secondary-color);
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
        }
        
        .btn-apply {
            background-color: var(--primary-color);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }
        
        .btn-apply:hover {
            background-color: var(--secondary-color);
            transform: translateY(-1px);
        }
        
        .feature-card {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: white;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: var(--accent-color);
            margin-bottom: 1.5rem;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }
        
        .why-us-section {
            background-color: var(--light-gray);
            padding: 80px 0;
        }
        
        .jobs-section {
            padding: 80px 0;
        }
        
        .footer {
            background-color: var(--secondary-color);
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .footer a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            transition: color 0.3s ease;
        }
        
        .footer a:hover {
            color: var(--accent-color);
        }
        
        .social-icons {
            font-size: 1.5rem;
            margin-top: 1rem;
        }
        
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .section-title {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-briefcase-fill me-2"></i>
                TantaHire
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">عنّا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#jobs">الوظائف</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">تواصل معنا</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">وظيفتك التالية في طنطا تبدأ من هنا!</h1>
                    <p class="hero-subtitle">
                        اكتشف أفضل الفرص الوظيفية في مدينة طنطا واحصل على الوظيفة التي تحلم بها
                    </p>
                    <a href="#jobs" class="btn btn-hero">
                        <i class="bi bi-search me-2"></i>
                        ابحث عن وظيفة
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Jobs Section -->
    <section id="jobs" class="jobs-section">
        <div class="container">
            <h2 class="section-title">الوظائف المتاحة حالياً</h2>
            
            <div class="row">
                <!-- Job Card 1 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مطور ويب Frontend</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة التقنيات المتقدمة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                مطلوب مطور ويب متخصص في React وVue.js للعمل في بيئة تقنية متطورة.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 2 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">محاسب مالي</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                مجموعة النيل التجارية
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                فرصة ممتازة للعمل كمحاسب مالي في شركة رائدة مع راتب تنافسي ومزايا إضافية.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 3 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مدير تسويق رقمي</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                وكالة الإبداع الرقمي
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام جزئي</span>
                            <p class="card-text mt-3">
                                انضم إلى فريقنا كمدير تسويق رقمي وساعد في نمو العلامات التجارية المحلية.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <a href="#" class="btn btn-outline-primary btn-lg">
                    عرض جميع الوظائف
                    <i class="bi bi-arrow-left me-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Why Us Section -->
    <section id="about" class="why-us-section">
        <div class="container">
            <h2 class="section-title">لماذا TantaHire؟</h2>

            <div class="row">
                <!-- Feature 1 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-lightning-charge"></i>
                        </div>
                        <h4 class="feature-title">سهولة الاستخدام</h4>
                        <p class="text-muted">
                            واجهة بسيطة وسهلة تمكنك من البحث عن الوظائف والتقديم عليها في دقائق معدودة
                        </p>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-arrow-clockwise"></i>
                        </div>
                        <h4 class="feature-title">تحديث دائم</h4>
                        <p class="text-muted">
                            نضيف وظائف جديدة يومياً ونحدث قاعدة البيانات باستمرار لضمان أحدث الفرص المتاحة
                        </p>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-collection"></i>
                        </div>
                        <h4 class="feature-title">فرص متنوعة</h4>
                        <p class="text-muted">
                            نوفر وظائف في جميع المجالات والتخصصات لتناسب مختلف المؤهلات والخبرات
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5">
        <div class="container">
            <h2 class="section-title">تواصل معنا</h2>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card border-0 shadow-lg">
                        <div class="card-body p-5">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-envelope"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">البريد الإلكتروني</h5>
                                            <p class="text-muted mb-0"><EMAIL></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-telephone"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">الهاتف</h5>
                                            <p class="text-muted mb-0">+20 40 123 4567</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-geo-alt"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">العنوان</h5>
                                            <p class="text-muted mb-0">طنطا، محافظة الغربية، مصر</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-clock"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">ساعات العمل</h5>
                                            <p class="text-muted mb-0">السبت - الخميس: 9:00 - 17:00</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h5 class="mb-3">
                        <i class="bi bi-briefcase-fill me-2"></i>
                        TantaHire
                    </h5>
                    <p class="mb-3">
                        منصة التوظيف الأولى في مدينة طنطا، نربط بين أصحاب العمل والباحثين عن الوظائف
                    </p>
                </div>

                <div class="col-lg-6">
                    <h6 class="mb-3">تابعنا على</h6>
                    <div class="social-icons">
                        <a href="#" class="me-3">
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-twitter"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-linkedin"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.2);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 TantaHire. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="me-3">سياسة الخصوصية</a>
                    <a href="#" class="me-3">الشروط والأحكام</a>
                    <a href="#">المساعدة</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Smooth Scrolling -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Active navigation highlighting
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Job application button functionality
        document.querySelectorAll('.btn-apply').forEach(button => {
            button.addEventListener('click', function() {
                alert('شكراً لاهتمامك! سيتم توجيهك إلى صفحة التقديم قريباً.');
            });
        });
    </script>
</body>
</html>
