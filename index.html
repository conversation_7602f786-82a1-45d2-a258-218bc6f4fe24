<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TantaHire - وظيفتك التالية في طنطا</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            direction: rtl;
            text-align: right;
            overflow-x: hidden;
        }

        :root {
            --primary-color: #2347a9;
            --secondary-color: #1f2937;
            --accent-color: #3b82f6;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --light-gray: #f8fafc;
            --dark-gray: #64748b;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 2rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .navbar {
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98) !important;
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .hero-section {
            background: var(--gradient-primary);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>');
            background-size: cover;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }
        
        .hero-title {
            font-size: 4rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            line-height: 1.2;
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.4rem;
            margin-bottom: 2.5rem;
            opacity: 0.95;
            font-weight: 300;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .btn-hero {
            background: white;
            color: var(--primary-color);
            border: none;
            padding: 18px 45px;
            font-size: 1.2rem;
            font-weight: 700;
            border-radius: 50px;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .btn-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-accent);
            transition: left 0.5s ease;
            z-index: -1;
        }

        .btn-hero:hover::before {
            left: 0;
        }

        .btn-hero:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(0,0,0,0.3);
            color: white;
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 3rem;
            text-align: center;
        }
        
        .job-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            margin-bottom: 2rem;
            background: white;
            position: relative;
            overflow: hidden;
        }

        .job-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .job-card:hover::before {
            transform: scaleX(1);
        }

        .job-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .job-title {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.3rem;
        }
        
        .company-name {
            color: var(--dark-gray);
            font-weight: 500;
        }
        
        .job-location {
            color: var(--accent-color);
            font-weight: 500;
        }
        
        .job-type {
            background: var(--gradient-accent);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .job-type.part-time {
            background: var(--gradient-secondary);
        }

        .job-type.remote {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .btn-apply {
            background: var(--gradient-primary);
            border: none;
            border-radius: 30px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-apply::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-secondary);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .btn-apply:hover::before {
            left: 0;
        }

        .btn-apply:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .feature-card {
            text-align: center;
            padding: 3rem 2rem;
            border-radius: 25px;
            background: white;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.4s ease;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-accent);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .feature-card:hover::before {
            opacity: 0.05;
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .feature-card > * {
            position: relative;
            z-index: 2;
        }
        
        .feature-icon {
            font-size: 4rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
            transition: transform 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }
        
        .why-us-section {
            background: linear-gradient(135deg, var(--light-gray) 0%, #e2e8f0 100%);
            padding: 100px 0;
            position: relative;
        }

        .why-us-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" fill="none"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        
        .jobs-section {
            padding: 100px 0;
            background: white;
        }

        .stats-section {
            background: var(--gradient-primary);
            color: white;
            padding: 80px 0;
            text-align: center;
        }

        .stat-item {
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .footer {
            background-color: var(--secondary-color);
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .footer a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            transition: color 0.3s ease;
        }
        
        .footer a:hover {
            color: var(--accent-color);
        }
        
        .social-icons {
            font-size: 1.5rem;
            margin-top: 1rem;
        }
        
        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .hero-title {
                font-size: 3.5rem;
            }

            .feature-card {
                padding: 2.5rem 2rem;
            }
        }

        @media (max-width: 992px) {
            .hero-title {
                font-size: 3rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .stats-section {
                padding: 60px 0;
            }

            .stat-number {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 768px) {
            .hero-section {
                padding: 100px 0 60px;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .section-title {
                font-size: 2rem;
                margin-bottom: 2rem;
            }

            .btn-hero {
                padding: 15px 35px;
                font-size: 1.1rem;
            }

            .feature-card {
                padding: 2rem 1.5rem;
                margin-bottom: 2rem;
            }

            .feature-icon {
                font-size: 3rem;
            }

            .jobs-section, .why-us-section {
                padding: 60px 0;
            }

            .stat-number {
                font-size: 2rem;
            }

            .stat-label {
                font-size: 1rem;
            }
        }

        @media (max-width: 576px) {
            .hero-title {
                font-size: 2rem;
            }

            .hero-subtitle {
                font-size: 1rem;
            }

            .btn-hero {
                padding: 12px 30px;
                font-size: 1rem;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .job-card {
                margin-bottom: 1.5rem;
            }

            .feature-card {
                padding: 1.5rem;
            }

            .feature-icon {
                font-size: 2.5rem;
            }

            .stat-item {
                padding: 1rem;
            }

            .stat-number {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-briefcase-fill me-2"></i>
                TantaHire
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item ">
                        <a class="nav-link active" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">عنّا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#jobs">الوظائف</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">تواصل معنا</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">وظيفتك التالية في طنطا تبدأ من هنا!</h1>
                    <p class="hero-subtitle">
                        اكتشف أفضل الفرص الوظيفية في مدينة طنطا واحصل على الوظيفة التي تحلم بها
                    </p>
                    <a href="#jobs" class="btn btn-hero">
                        <i class="bi bi-search me-2"></i>
                        ابحث عن وظيفة
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Jobs Section -->
    <section id="jobs" class="jobs-section">
        <div class="container">
            <h2 class="section-title">الوظائف المتاحة حالياً</h2>
            
            <div class="row">
                <!-- Job Card 1 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مطور ويب Frontend</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة التقنيات المتقدمة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                مطلوب مطور ويب متخصص في React وVue.js للعمل في بيئة تقنية متطورة.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 2 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">محاسب مالي</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                مجموعة النيل التجارية
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                فرصة ممتازة للعمل كمحاسب مالي في شركة رائدة مع راتب تنافسي ومزايا إضافية.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 3 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مدير تسويق رقمي</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                وكالة الإبداع الرقمي
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type part-time mb-3">دوام جزئي</span>
                            <p class="card-text mt-3">
                                انضم إلى فريقنا كمدير تسويق رقمي وساعد في نمو العلامات التجارية المحلية.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 4 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مصمم جرافيك</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                استوديو الفن الحديث
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                مطلوب مصمم جرافيك مبدع للعمل على مشاريع متنوعة في بيئة إبداعية محفزة.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 5 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مدرس لغة إنجليزية</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                معهد اللغات الدولي
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type part-time mb-3">دوام جزئي</span>
                            <p class="card-text mt-3">
                                فرصة ممتازة لتدريس اللغة الإنجليزية للطلاب من مختلف الأعمار والمستويات.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 6 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مطور تطبيقات موبايل</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة التطبيقات الذكية
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية / عن بُعد
                            </p>
                            <span class="job-type remote mb-3">عن بُعد</span>
                            <p class="card-text mt-3">
                                انضم لفريقنا لتطوير تطبيقات موبايل مبتكرة باستخدام React Native وFlutter.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 7 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مهندس مدني</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة المقاولات المتطورة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                مطلوب مهندس مدني خبرة 2-5 سنوات للعمل على مشاريع إنشائية متنوعة.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 8 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">صيدلي</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                صيدلية النور الطبية
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                فرصة عمل ممتازة لصيدلي مؤهل في صيدلية حديثة ومجهزة بأحدث التقنيات.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Job Card 9 -->
                <div class="col-lg-4 col-md-6">
                    <div class="card job-card">
                        <div class="card-body">
                            <h5 class="job-title">مندوب مبيعات</h5>
                            <p class="company-name mb-2">
                                <i class="bi bi-building me-2"></i>
                                شركة التوزيع الحديثة
                            </p>
                            <p class="job-location mb-2">
                                <i class="bi bi-geo-alt me-2"></i>
                                طنطا، الغربية
                            </p>
                            <span class="job-type mb-3">دوام كامل</span>
                            <p class="card-text mt-3">
                                انضم لفريق المبيعات النشط واحصل على راتب أساسي + عمولة مجزية.
                            </p>
                            <button class="btn btn-apply btn-primary w-100">
                                <i class="bi bi-send me-2"></i>
                                تقدم الآن
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <a href="#" class="btn btn-outline-primary btn-lg px-5 py-3">
                    <i class="bi bi-search me-2"></i>
                    عرض جميع الوظائف
                    <i class="bi bi-arrow-left ms-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="1250">0</span>
                        <div class="stat-label">وظيفة متاحة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="850">0</span>
                        <div class="stat-label">شركة مسجلة</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="5200">0</span>
                        <div class="stat-label">باحث عن عمل</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="3400">0</span>
                        <div class="stat-label">وظيفة تم شغلها</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Us Section -->
    <section id="about" class="why-us-section">
        <div class="container">
            <h2 class="section-title">لماذا TantaHire؟</h2>

            <div class="row">
                <!-- Feature 1 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-lightning-charge"></i>
                        </div>
                        <h4 class="feature-title">سهولة الاستخدام</h4>
                        <p class="text-muted">
                            واجهة بسيطة وسهلة تمكنك من البحث عن الوظائف والتقديم عليها في دقائق معدودة
                        </p>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-arrow-clockwise"></i>
                        </div>
                        <h4 class="feature-title">تحديث دائم</h4>
                        <p class="text-muted">
                            نضيف وظائف جديدة يومياً ونحدث قاعدة البيانات باستمرار لضمان أحدث الفرص المتاحة
                        </p>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-collection"></i>
                        </div>
                        <h4 class="feature-title">فرص متنوعة</h4>
                        <p class="text-muted">
                            نوفر وظائف في جميع المجالات والتخصصات لتناسب مختلف المؤهلات والخبرات
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5">
        <div class="container">
            <h2 class="section-title">تواصل معنا</h2>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card border-0 shadow-lg">
                        <div class="card-body p-5">
                            <div class="row">
                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-envelope"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">البريد الإلكتروني</h5>
                                            <p class="text-muted mb-0"><EMAIL></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-telephone"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">الهاتف</h5>
                                            <p class="text-muted mb-0">+20 40 123 4567</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-geo-alt"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">العنوان</h5>
                                            <p class="text-muted mb-0">طنطا، محافظة الغربية، مصر</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-4">
                                    <div class="d-flex align-items-center">
                                        <div class="feature-icon me-3" style="font-size: 2rem;">
                                            <i class="bi bi-clock"></i>
                                        </div>
                                        <div>
                                            <h5 class="mb-1">ساعات العمل</h5>
                                            <p class="text-muted mb-0">السبت - الخميس: 9:00 - 17:00</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <h5 class="mb-3">
                        <i class="bi bi-briefcase-fill me-2"></i>
                        TantaHire
                    </h5>
                    <p class="mb-3">
                        منصة التوظيف الأولى في مدينة طنطا، نربط بين أصحاب العمل والباحثين عن الوظائف
                    </p>
                </div>

                <div class="col-lg-6">
                    <h6 class="mb-3">تابعنا على</h6>
                    <div class="social-icons">
                        <a href="#" class="me-3">
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-twitter"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-linkedin"></i>
                        </a>
                        <a href="#" class="me-3">
                            <i class="bi bi-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>

            <hr class="my-4" style="border-color: rgba(255,255,255,0.2);">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 TantaHire. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="me-3">سياسة الخصوصية</a>
                    <a href="#" class="me-3">الشروط والأحكام</a>
                    <a href="#">المساعدة</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Enhanced JavaScript -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            // Navbar background change
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Active navigation highlighting
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // Animated counter for stats
        function animateCounter(element) {
            const target = parseInt(element.getAttribute('data-count'));
            const increment = target / 100;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 20);
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Animate stats counters
                    if (entry.target.classList.contains('stats-section')) {
                        const counters = entry.target.querySelectorAll('.stat-number');
                        counters.forEach(counter => {
                            animateCounter(counter);
                        });
                    }

                    // Add fade-in animation to job cards
                    if (entry.target.classList.contains('job-card')) {
                        entry.target.style.opacity = '0';
                        entry.target.style.transform = 'translateY(30px)';
                        setTimeout(() => {
                            entry.target.style.transition = 'all 0.6s ease';
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, 100);
                    }

                    // Add fade-in animation to feature cards
                    if (entry.target.classList.contains('feature-card')) {
                        entry.target.style.opacity = '0';
                        entry.target.style.transform = 'translateY(30px)';
                        setTimeout(() => {
                            entry.target.style.transition = 'all 0.6s ease';
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                        }, 200);
                    }

                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe elements
        document.addEventListener('DOMContentLoaded', function() {
            // Observe stats section
            const statsSection = document.querySelector('.stats-section');
            if (statsSection) observer.observe(statsSection);

            // Observe job cards
            document.querySelectorAll('.job-card').forEach(card => {
                observer.observe(card);
            });

            // Observe feature cards
            document.querySelectorAll('.feature-card').forEach(card => {
                observer.observe(card);
            });
        });

        // Enhanced job application functionality
        document.querySelectorAll('.btn-apply').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // Add loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري التحميل...';
                this.disabled = true;

                // Simulate API call
                setTimeout(() => {
                    this.innerHTML = '<i class="bi bi-check-circle me-2"></i>تم الإرسال!';
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-success');

                    // Show success message
                    const jobTitle = this.closest('.job-card').querySelector('.job-title').textContent;
                    showNotification(`تم إرسال طلبك لوظيفة "${jobTitle}" بنجاح!`, 'success');

                    // Reset button after 3 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                        this.classList.remove('btn-success');
                        this.classList.add('btn-primary');
                    }, 3000);
                }, 1500);
            });
        });

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} position-fixed`;
            notification.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                border: none;
                border-radius: 10px;
                animation: slideInRight 0.5s ease;
            `;
            notification.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.5s ease';
                    setTimeout(() => notification.remove(), 500);
                }
            }, 5000);
        }

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
