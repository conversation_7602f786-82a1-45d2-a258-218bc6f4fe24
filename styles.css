/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Exo 2', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    position: relative;
}

.logo-text {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-orbit {
    width: 30px;
    height: 30px;
    border: 2px solid #00d4ff;
    border-radius: 50%;
    margin-left: 10px;
    position: relative;
    animation: orbit 3s linear infinite;
}

.logo-orbit::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: #ff00ff;
    border-radius: 50%;
    box-shadow: 0 0 10px #ff00ff;
}

@keyframes orbit {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #00d4ff;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #0a0a0a 100%);
    overflow: hidden;
}

/* Animated Stars Background */
.stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: twinkle 4s ease-in-out infinite alternate;
}

@keyframes twinkle {
    0% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* Floating Planets */
.floating-planets {
    position: absolute;
    width: 100%;
    height: 100%;
}

.planet {
    position: absolute;
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.planet-1 {
    width: 80px;
    height: 80px;
    background: radial-gradient(circle at 30% 30%, #ff6b6b, #d63031);
    top: 20%;
    right: 10%;
    animation-delay: 0s;
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
}

.planet-2 {
    width: 60px;
    height: 60px;
    background: radial-gradient(circle at 30% 30%, #74b9ff, #0984e3);
    bottom: 30%;
    left: 15%;
    animation-delay: -2s;
    box-shadow: 0 0 25px rgba(116, 185, 255, 0.3);
}

.planet-3 {
    width: 40px;
    height: 40px;
    background: radial-gradient(circle at 30% 30%, #a29bfe, #6c5ce7);
    top: 60%;
    right: 20%;
    animation-delay: -4s;
    box-shadow: 0 0 20px rgba(162, 155, 254, 0.3);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Hero Content */
.hero-content {
    text-align: center;
    z-index: 10;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.title-line {
    display: block;
    background: linear-gradient(45deg, #00d4ff, #ff00ff, #00ff88);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    0% { text-shadow: 0 0 20px rgba(0, 212, 255, 0.5); }
    100% { text-shadow: 0 0 30px rgba(255, 0, 255, 0.8); }
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 0.9;
        transform: translateY(0);
    }
}

/* CTA Button */
.cta-button {
    position: relative;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    font-family: 'Orbitron', monospace;
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffffff;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.cta-button:hover .button-glow {
    left: 100%;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 212, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 212, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 212, 255, 0); }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 20px;
    height: 20px;
    border-right: 2px solid #00d4ff;
    border-bottom: 2px solid #00d4ff;
    transform: rotate(45deg);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* Sections */
.destinations, .about, .contact {
    padding: 100px 0;
    position: relative;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Destinations Section */
.destinations {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.destination-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.destination-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.destination-card:hover::before {
    left: 100%;
}

.card-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    position: relative;
}

.mars {
    background: radial-gradient(circle at 30% 30%, #ff6b6b, #d63031);
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.3);
}

.europa {
    background: radial-gradient(circle at 30% 30%, #74b9ff, #0984e3);
    box-shadow: 0 0 30px rgba(116, 185, 255, 0.3);
}

.titan {
    background: radial-gradient(circle at 30% 30%, #fdcb6e, #e17055);
    box-shadow: 0 0 30px rgba(253, 203, 110, 0.3);
}

.destination-card h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #00d4ff;
}

.destination-card p {
    margin-bottom: 1.5rem;
    opacity: 0.8;
    line-height: 1.6;
}

.card-price {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    color: #ff00ff;
}

/* About Section */
.about {
    background: #0a0a0a;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    text-align: left;
    margin-bottom: 2rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.features-list {
    list-style: none;
}

.features-list li {
    padding: 0.5rem 0;
    font-size: 1.1rem;
    opacity: 0.9;
}

.about-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.spacecraft {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    border-radius: 50%;
    position: relative;
    animation: rotate 10s linear infinite;
}

.spacecraft::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 150px;
    height: 150px;
    background: #0a0a0a;
    border-radius: 50%;
}

.spacecraft::after {
    content: '🚀';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Contact Section */
.contact {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    text-align: center;
}

.contact-subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.8;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
}

.contact-icon {
    font-size: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .nav-menu {
        display: none;
    }

    .planet-1, .planet-2, .planet-3 {
        display: none;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-info {
        flex-direction: column;
        gap: 1.5rem;
    }

    .destinations-grid {
        grid-template-columns: 1fr;
    }
}
