/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgb(10, 10, 10);
    backdrop-filter: blur(50px);
    -webkit-backdrop-filter: blur(50px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.8);
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgb(10, 10, 10) 0%, rgb(26, 26, 46) 100%);
    z-index: -1;
    backdrop-filter: blur(60px);
    -webkit-backdrop-filter: blur(60px);
}

.navbar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgb(10, 10, 10);
    z-index: -2;
    opacity: 0.95;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 10;
}

.logo {
    display: flex;
    align-items: center;
    position: relative;
}

.logo-text {
    font-family: 'Cairo', sans-serif;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-orbit {
    width: 30px;
    height: 30px;
    border: 2px solid #00d4ff;
    border-radius: 50%;
    margin-right: 10px;
    position: relative;
    animation: orbit 3s linear infinite;
}

.logo-orbit::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: #ff00ff;
    border-radius: 50%;
    box-shadow: 0 0 10px #ff00ff;
}

@keyframes orbit {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #00d4ff;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    right: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Mobile menu button - hidden by default */
.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    position: relative;
    z-index: 1001;
}

.menu-line {
    width: 25px;
    height: 3px;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-btn.active .menu-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active .menu-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .menu-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 10;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg,
        rgba(10, 10, 10, 0.99) 0%,
        rgba(26, 26, 46, 0.99) 25%,
        rgba(16, 21, 62, 0.99) 50%,
        rgba(26, 26, 46, 0.99) 75%,
        rgba(10, 10, 10, 0.99) 100%);
    backdrop-filter: blur(80px);
    -webkit-backdrop-filter: blur(80px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s ease;
    overflow: hidden;
}

.mobile-menu-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(0, 212, 255, 0.05) 0%,
        rgba(255, 0, 255, 0.05) 25%,
        rgba(0, 255, 136, 0.05) 50%,
        rgba(255, 0, 255, 0.05) 75%,
        rgba(0, 212, 255, 0.05) 100%);
    z-index: -1;
    animation: gradientMove 10s ease-in-out infinite;
}

@keyframes gradientMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-10px) translateY(-10px); }
    50% { transform: translateX(10px) translateY(10px); }
    75% { transform: translateX(-5px) translateY(5px); }
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 2rem;
    position: relative;
    background: linear-gradient(135deg,
        rgba(10, 10, 10, 0.98) 0%,
        rgba(26, 26, 46, 0.98) 30%,
        rgba(16, 21, 62, 0.98) 60%,
        rgba(10, 10, 10, 0.98) 100%);
    backdrop-filter: blur(60px);
    -webkit-backdrop-filter: blur(60px);
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    width: 90%;
    max-width: 300px;
}

.mobile-menu-header h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.5rem;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.mobile-menu-close {
    background: none;
    border: none;
    color: #00d4ff;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.mobile-menu-close:hover {
    background: rgba(0, 212, 255, 0.1);
    transform: rotate(90deg);
}

.mobile-nav {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem 1rem;
    width: 100%;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.12);
    border-radius: 50px;
    border: 2px solid rgba(0, 212, 255, 0.4);
    color: #ffffff;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    transform: translateY(30px);
    opacity: 0;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    width: 80%;
    max-width: 300px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
    min-height: 55px;
}

.mobile-menu-overlay.active .mobile-nav-link {
    transform: translateY(0);
    opacity: 1;
}

.mobile-nav-link:nth-child(1) { transition-delay: 0.1s; }
.mobile-nav-link:nth-child(2) { transition-delay: 0.2s; }
.mobile-nav-link:nth-child(3) { transition-delay: 0.3s; }
.mobile-nav-link:nth-child(4) { transition-delay: 0.4s; }
.mobile-nav-link:nth-child(5) { transition-delay: 0.5s; }

.mobile-nav-link:hover,
.mobile-nav-link:active {
    background: linear-gradient(45deg, rgba(0, 212, 255, 0.3), rgba(255, 0, 255, 0.2));
    border-color: rgba(0, 212, 255, 0.8);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.mobile-nav-link i {
    font-size: 1.2rem;
    color: #00d4ff;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.mobile-menu-footer {
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    width: 90%;
    max-width: 300px;
}

.mobile-social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.mobile-social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00d4ff;
    text-decoration: none;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.mobile-social-link:hover {
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Hero Section */
.hero {
    margin-top: 50px;
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #0a0a0a 100%);
    overflow: hidden;
    padding: 80px 20px 20px;
}

/* Animated Stars Background */
.stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: twinkle 4s ease-in-out infinite alternate;
    z-index: 0;
}

@keyframes twinkle {
    0% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* Floating Code Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.code-element {
    position: absolute;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1.5rem;
    color: #00d4ff;
    animation: float 6s ease-in-out infinite;
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    user-select: none;
    opacity: 0.8;
}

.element-1 {
    top: 15%;
    right: 10%;
    animation-delay: 0s;
    font-size: 2rem;
}

.element-2 {
    bottom: 25%;
    left: 8%;
    animation-delay: -2s;
    color: #ff00ff;
    text-shadow: 0 0 20px rgba(255, 0, 255, 0.5);
}

.element-3 {
    top: 55%;
    right: 15%;
    animation-delay: -4s;
    color: #00ff88;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.element-4 {
    top: 35%;
    left: 5%;
    animation-delay: -1s;
    color: #ffff00;
    text-shadow: 0 0 20px rgba(255, 255, 0, 0.5);
}

.element-5 {
    bottom: 15%;
    right: 25%;
    animation-delay: -3s;
    color: #ff6b6b;
    text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(10deg); }
}

/* Hero Content */
.hero-content {
    text-align: center;
    z-index: 10;
    max-width: 800px;
    padding: 0 20px;
    position: relative;
    width: 100%;
    padding-bottom: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.profile-image {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto 2rem;
    z-index: 5;
    padding: 4px;
    background: linear-gradient(45deg, #00d4ff, #ff00ff, #00ff88);
    border-radius: 50%;
    transition: transform 0.6s ease;
    cursor: pointer;
}

.profile-image:hover {
    animation: rotate-border 0.8s ease-in-out;
}

@keyframes rotate-border {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.profile-img {
    width: calc(100% - 8px);
    height: calc(100% - 8px);
    border-radius: 50%;
    object-fit: cover;
    position: relative;
    z-index: 3;
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    background: #0a0a0a;
    transition: all 0.3s ease;
    margin: 4px;
}

.profile-image:hover .profile-img {
    box-shadow: 0 0 40px rgba(0, 212, 255, 0.7);
    transform: scale(1.02);
}

.image-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    position: relative;
    z-index: 3;
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
}

.image-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    opacity: 0.3;
    animation: pulse 2s infinite;
    z-index: 2;
    transition: all 0.3s ease;
}

.profile-image:hover .image-glow {
    opacity: 0.6;
    transform: scale(1.1);
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.1; }
    100% { transform: scale(1); opacity: 0.3; }
}

.hero-title {
    font-family: 'Cairo', sans-serif;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.title-line {
    display: block;
    font-size: 3.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #00d4ff, #ff00ff, #00ff88);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 2s ease-in-out infinite alternate;
    margin-bottom: 0.5rem;
}

.title-subtitle {
    display: block;
    font-size: 1.5rem;
    font-weight: 400;
    color: #00d4ff;
    margin-top: 0.5rem;
    opacity: 0.9;
}

@keyframes glow {
    0% { text-shadow: 0 0 20px rgba(0, 212, 255, 0.5); }
    100% { text-shadow: 0 0 30px rgba(255, 0, 255, 0.8); }
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 0.9;
        transform: translateY(0);
    }
}

/* Hero Buttons */
.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.cta-button {
    position: relative;
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    font-family: 'Cairo', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 180px;
    z-index: 10;
}

.cta-button.primary {
    background: linear-gradient(45deg, #00e5ff, #ff1493, #00ff7f);
    background-size: 200% 200%;
    color: #ffffff;
    animation: gradientShift 3s ease infinite, buttonPulse 2s infinite;
    box-shadow:
        0 0 20px rgba(0, 229, 255, 0.6),
        0 0 40px rgba(255, 20, 147, 0.4),
        0 4px 15px rgba(0, 255, 127, 0.3);
    font-weight: 700;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.cta-button.primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.cta-button.primary:hover::before {
    left: 100%;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes buttonPulse {
    0% {
        box-shadow:
            0 0 0 0 rgba(0, 229, 255, 0.8),
            0 0 20px rgba(0, 229, 255, 0.6),
            0 0 40px rgba(255, 20, 147, 0.4);
    }
    50% {
        box-shadow:
            0 0 0 8px rgba(0, 229, 255, 0.3),
            0 0 30px rgba(0, 229, 255, 0.8),
            0 0 60px rgba(255, 20, 147, 0.6);
    }
    100% {
        box-shadow:
            0 0 0 0 rgba(0, 229, 255, 0),
            0 0 20px rgba(0, 229, 255, 0.6),
            0 0 40px rgba(255, 20, 147, 0.4);
    }
}

.cta-button.secondary {
    background: transparent;
    color: #00d4ff;
    border: 2px solid #00d4ff;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
}

.cta-button:hover {
    transform: translateY(-3px);
}

.cta-button.primary:hover {
    box-shadow:
        0 0 30px rgba(0, 229, 255, 0.8),
        0 0 60px rgba(255, 20, 147, 0.6),
        0 10px 40px rgba(0, 255, 127, 0.5),
        inset 0 0 20px rgba(255, 255, 255, 0.2);
    transform: translateY(-3px) scale(1.05);
    background: linear-gradient(45deg, #00ffff, #ff1493, #00ff7f, #00e5ff);
    background-size: 300% 300%;
    animation: gradientShift 1s ease infinite, pulse 1s infinite;
    border: 2px solid rgba(255, 255, 255, 0.6);
    text-shadow: 0 0 15px rgba(255, 255, 255, 1);
}

.cta-button.secondary:hover {
    background: #00d4ff;
    color: #0a0a0a;
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
}

.button-glow {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
    pointer-events: none;
}

.cta-button:hover .button-glow {
    left: 100%;
}

.button-text {
    position: relative;
    z-index: 2;
    pointer-events: none;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
    cursor: pointer;
    z-index: 10;
    padding: 12px;
    border-radius: 50%;
    background: rgba(0, 212, 255, 0.15);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(0, 212, 255, 0.4);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 212, 255, 0.2);
}

.scroll-indicator:hover {
    background: rgba(0, 212, 255, 0.2);
    transform: translateX(-50%) scale(1.1);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.scroll-arrow {
    width: 20px;
    height: 20px;
    border-right: 3px solid #00d4ff;
    border-bottom: 3px solid #00d4ff;
    transform: rotate(45deg);
    position: relative;
}

.scroll-arrow::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    width: 20px;
    height: 20px;
    border-right: 2px solid rgba(0, 212, 255, 0.5);
    border-bottom: 2px solid rgba(0, 212, 255, 0.5);
    transform: rotate(45deg);
    animation: arrowPulse 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@keyframes arrowPulse {
    0%, 100% { opacity: 0; transform: rotate(45deg) scale(0.8); }
    50% { opacity: 1; transform: rotate(45deg) scale(1.2); }
}

/* Sections */
.about, .skills, .projects, .contact {
    padding: 100px 0;
    position: relative;
}

.section-title {
    font-family: 'Cairo', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* About Section */
.about {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-intro {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.experience-stats {
    display: flex;
    gap: 2rem;
    justify-content: space-around;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    color: #00d4ff;
    display: block;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.about-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.code-animation {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    padding: 2rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
    font-family: 'Orbitron', monospace;
    font-size: 0.9rem;
    line-height: 1.8;
}

.code-line {
    margin-bottom: 0.5rem;
    animation: typewriter 0.5s ease-in-out;
}

.code-line.indent {
    padding-right: 2rem;
}

.code-keyword { color: #ff00ff; }
.code-variable { color: #00d4ff; }
.code-operator { color: #ffffff; }
.code-string { color: #00ff88; }
.code-array { color: #ffff00; }
.code-function { color: #ff6b6b; }
.code-brackets { color: #ffffff; }

@keyframes typewriter {
    from { opacity: 0; transform: translateX(20px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Skills Section */
.skills {
    background: #0a0a0a;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.skill-category {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.skill-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 212, 255, 0.2);
}

.category-title {
    font-family: 'Cairo', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #00d4ff;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.category-title i {
    font-size: 1.5rem;
}

.skill-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.skill-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.skill-name {
    font-weight: 600;
    color: #ffffff;
}

.skill-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    border-radius: 4px;
    width: 0;
    transition: width 1s ease-in-out;
    position: relative;
}

.skill-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Projects Section */
.projects {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
}

.project-image {
    height: 200px;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    position: relative;
    overflow: hidden;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(0, 212, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00d4ff;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid #00d4ff;
}

.project-link:hover {
    background: #00d4ff;
    color: #0a0a0a;
    transform: scale(1.1);
}

.project-content {
    padding: 2rem;
}

.project-title {
    font-family: 'Cairo', sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #00d4ff;
    margin-bottom: 1rem;
}

.project-description {
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.project-tech {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tech-tag {
    background: rgba(0, 212, 255, 0.2);
    color: #00d4ff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(0, 212, 255, 0.3);
}

/* Empty Projects State */
.empty-projects {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    color: white;
    animation: pulse 2s infinite;
}

.empty-projects h3 {
    font-family: 'Cairo', sans-serif;
    font-size: 1.5rem;
    color: #00d4ff;
    margin-bottom: 1rem;
}

.empty-projects p {
    opacity: 0.8;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    background: #0a0a0a;
    text-align: center;
}

.contact-subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.8;
}

.contact-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    width: 100%;
    max-width: 800px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.2);
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.contact-details {
    text-align: right;
}

.contact-details h4 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    color: #00d4ff;
    margin-bottom: 0.5rem;
}

.contact-details span {
    opacity: 0.9;
}

.social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.social-link {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00d4ff;
    text-decoration: none;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(0, 212, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #00d4ff, #ff00ff);
    transition: left 0.3s ease;
    z-index: -1;
}

.social-link:hover::before {
    left: 0;
}

.social-link:hover {
    color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: 100px 15px 60px;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-content {
        padding: 0 15px;
        width: 100%;
        max-width: 400px;
        text-align: center;
    }

    .hero-title {
        margin-bottom: 1.5rem;
    }

    .hero-title .title-line {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 0.5rem;
    }

    .hero-title .title-subtitle {
        font-size: 1rem;
        margin-top: 0.5rem;
        opacity: 0.9;
    }

    .hero-description {
        font-size: 0.9rem;
        padding: 0 5px;
        line-height: 1.6;
        margin-bottom: 2rem;
        opacity: 0.8;
    }

    .profile-image {
        width: 120px;
        height: 120px;
        margin-bottom: 1.5rem;
    }

    .nav-menu {
        display: none;
    }

    /* Add mobile menu button */
    .mobile-menu-btn {
        display: flex;
    }

    .navbar {
        padding: 0.8rem 0;
        background: rgb(10, 10, 10) !important;
        backdrop-filter: blur(80px) !important;
        -webkit-backdrop-filter: blur(80px) !important;
        border-bottom: 2px solid rgba(0, 212, 255, 0.2);
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.9);
    }

    .navbar::before {
        background: rgb(10, 10, 10) !important;
        backdrop-filter: blur(100px) !important;
        -webkit-backdrop-filter: blur(100px) !important;
    }

    .navbar::after {
        background: rgb(10, 10, 10) !important;
        opacity: 1 !important;
    }

    .nav-container {
        background: none !important;
        padding: 0 15px;
    }

    .logo-text {
        font-size: 1.5rem;
    }

    .floating-elements .code-element {
        font-size: 0.8rem;
        opacity: 0.4;
        animation-duration: 8s;
    }

    .element-1 {
        font-size: 1rem;
        top: 15%;
        right: 8%;
    }

    .element-2 {
        bottom: 25%;
        left: 8%;
        font-size: 0.8rem;
    }

    .element-3 {
        top: 55%;
        right: 12%;
        font-size: 0.8rem;
    }

    .element-4 {
        top: 35%;
        left: 5%;
        font-size: 0.7rem;
    }

    .element-5 {
        bottom: 15%;
        right: 25%;
        font-size: 0.7rem;
    }

    .about, .skills, .projects, .contact {
        padding: 60px 0;
    }

    .container {
        padding: 0 15px;
    }

    .section-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .experience-stats {
        flex-direction: row;
        justify-content: space-around;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .stat-item {
        flex: 1;
        min-width: 80px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .code-animation {
        font-size: 0.75rem;
        padding: 1.5rem;
        margin-top: 1rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .skill-category {
        padding: 1.5rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .empty-projects {
        padding: 3rem 1.5rem;
    }

    .empty-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .contact-info {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-item {
        padding: 1.2rem;
        flex-direction: row;
        text-align: right;
    }

    .social-links {
        gap: 0.8rem;
    }

    .social-link {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        width: 100%;
        margin-top: 1.5rem;
    }

    .cta-button {
        width: 100%;
        max-width: 260px;
        padding: 14px 25px;
        font-size: 0.95rem;
        border-radius: 25px;
    }

    .cta-button.primary {
        margin-bottom: 0.5rem;
    }

    .scroll-indicator {
        bottom: 20px;
        padding: 8px;
        background: rgba(0, 212, 255, 0.2);
        border: 1px solid rgba(0, 212, 255, 0.4);
    }

    .scroll-arrow {
        width: 14px;
        height: 14px;
        border-width: 2px;
    }

    .hero-content {
        padding-bottom: 70px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 90px 10px 40px;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hero-content {
        padding: 0 10px;
        width: 100%;
        max-width: 350px;
    }

    .section-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .hero-title {
        margin-bottom: 1.2rem;
    }

    .hero-title .title-line {
        font-size: 1.7rem;
        line-height: 1.3;
    }

    .hero-title .title-subtitle {
        font-size: 0.9rem;
        margin-top: 0.3rem;
    }

    .profile-image {
        width: 100px;
        height: 100px;
        margin-bottom: 1rem;
    }

    .profile-img {
        width: calc(100% - 6px);
        height: calc(100% - 6px);
        margin: 3px;
    }

    .image-placeholder {
        font-size: 1.5rem;
    }

    .hero-description {
        font-size: 0.8rem;
        padding: 0 5px;
        line-height: 1.5;
        margin-bottom: 1.5rem;
    }

    .logo-text {
        font-size: 1.3rem;
    }

    .navbar {
        
        background: rgb(10, 10, 10) !important;
        backdrop-filter: blur(100px) !important;
        border-bottom: 2px solid rgba(0, 212, 255, 0.3);
        box-shadow: 0 6px 40px rgba(0, 0, 0, 0.95);
    }
    .nav-container{
        display: flex;
        justify-content: space-around;
        align-items: center;

    }
    .nav-container .nav-menu{
        width: 50%;
    }
    .navbar::before {
        background: rgb(10, 10, 10) !important;
        backdrop-filter: blur(120px) !important;
    }

    .navbar::after {
        background: rgb(10, 10, 10) !important;
        opacity: 1 !important;
    }

    .floating-elements .code-element {
        font-size: 0.7rem;
        opacity: 0.4;
    }

    .element-1 {
        font-size: 0.9rem;
        top: 8%;
        right: 3%;
    }

    .element-2, .element-3, .element-4, .element-5 {
        font-size: 0.7rem;
    }

    .about, .skills, .projects, .contact {
        padding: 50px 0;
    }

    .container {
        padding: 0 10px;
    }
    .stars{
        width: 100vh;
    }
    .experience-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .stat-item {
        width: 100%;
        max-width: 120px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .code-animation {
        font-size: 0.65rem;
        padding: 1rem;
        line-height: 1.4;
    }

    .skill-category {
        padding: 1.2rem;
    }

    .category-title {
        font-size: 1.1rem;
    }

    .empty-projects {
        padding: 2.5rem 1rem;
    }

    .empty-projects h3 {
        font-size: 1.3rem;
    }

    .empty-projects p {
        font-size: 0.95rem;
    }

    .empty-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
    }

    .contact-item {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.8rem;
    }

    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .social-links {
        gap: 0.6rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .social-link {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .cta-button {
        padding: 12px 20px;
        font-size: 0.9rem;
        max-width: 240px;
        border-radius: 20px;
    }

    .hero-buttons {
        gap: 0.8rem;
        margin-top: 1.2rem;
    }

    .floating-elements .code-element {
        font-size: 0.7rem;
        opacity: 0.3;
    }

    .element-1 {
        font-size: 0.9rem;
    }

    .scroll-indicator {
        bottom: 10px;
        padding: 8px;
    }

    .hero-content {
        padding-bottom: 70px;
    }

    .mobile-nav-link {
        width: 90%;
        max-width: 280px;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-height: 50px;
    }

    .mobile-nav {
        gap: 1.2rem;
        padding: 1.5rem 1rem;
    }
}

/* Animation Classes for JavaScript */
.fade-in {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease-out;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease-out;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Touch-friendly improvements */
@media (max-width: 768px) {
    /* Larger touch targets */
    .cta-button, .social-link, .project-link {
        min-height: 44px;
        min-width: 44px;
    }

    /* Prevent zoom on input focus */
    input, textarea, select {
        font-size: 16px;
    }

    /* Smooth scrolling for mobile */
    html {
        -webkit-overflow-scrolling: touch;
    }

    /* Prevent text selection on interactive elements */
    .cta-button, .nav-link, .social-link {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: transparent;
    }
}
@media (max-width: 768px) {
   .hero-content{
   
   }
    }